"""
Product Repository - Database operations for product management.
Handles all database interactions for product operations.
"""

import logging
import mysql.connector
from typing import Dict, Optional, List, Any
from datetime import datetime
from uuid import uuid4
from decimal import Decimal
from os import getenv
from dotenv import load_dotenv
import random
from managers.timeline_logger import log_timeline_event
from infrastructure.external_apis.jetveo_client import product_change_status_async
from infrastructure.external_apis.jetveo_client import product_change_status_async

load_dotenv()

logger = logging.getLogger(__name__)

class ProductRepository:
    """Repository for product database operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def _get_db_connection(self):
        """Get database connection"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def find_product_for_pickup(
        self,
        section_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,
        product_uuid: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Find product for pickup by section_id, reservation_pin, or product_uuid.

        Args:
            section_id: Section ID to search in (customer pickup without PIN)
            reservation_pin: Reservation PIN to search by (customer pickup with PIN)
            product_uuid: Product UUID to search by

        Returns:
            Product record if found, None otherwise

        Note:
            - If product is reserved (reserved = 1), PIN is required
            - If product is not reserved, it can be picked up by section_id only
            - UUID search returns any product with status = 1
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            if product_uuid is not None:
                # Find by product UUID
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE uuid = %s AND status = 1
                """, (product_uuid,))
            elif reservation_pin is not None:
                # Find by reservation_pin (customer pickup with PIN)
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE reservation_pin = %s AND status = 1 AND reserved = 1
                """, (reservation_pin,))
            elif section_id is not None:
                # Find by section_id (customer pickup without PIN - only for non-reserved products)
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE section_id = %s AND status = 1 AND reserved = 0
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (str(section_id),))
            else:
                return None

            return cursor.fetchone()

        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding product for pickup: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def find_reserved_product_in_section(self, section_id: int) -> Optional[Dict[str, Any]]:
        """
        Find reserved product in a specific section.
        
        Args:
            section_id: Section ID to search in
            
        Returns:
            Reserved product record if found, None otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE section_id = %s AND status = 1 AND reserved = 1
                ORDER BY created_at DESC
                LIMIT 1
            """, (str(section_id),))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding reserved product in section: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def check_section_has_active_product(self, section_id: int) -> bool:
        """
        Check if section already has an active product.
        
        Args:
            section_id: Section ID to check
            
        Returns:
            True if section has active product, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT id FROM sale_reservations 
                WHERE section_id = %s AND status != 0
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            return existing_product is not None
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error checking section: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def insert_custom_product(
        self, 
        section_id: int, 
        price: Decimal
    ) -> Optional[Dict[str, Any]]:
        """
        Insert a custom product into database.
        
        Args:
            section_id: Section ID where product will be placed
            price: Product price
            
        Returns:
            Inserted product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Generate new UUID
            new_uuid = str(uuid4())
            
            # Insert new product
            cursor.execute("""
                INSERT INTO sale_reservations 
                (uuid, section_id, price, status, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, 'custom', %s, %s)
            """, (
                new_uuid,
                section_id,
                float(price),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()
            
            product_change_status_async(
                reservation_uuid=new_uuid,
                section_id=section_id,
                price=float(price),
                action=1,
                status=1
            )


            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE uuid = %s
            """, (new_uuid,))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error inserting custom product: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def insert_product_with_ean(
        self, 
        section_id: int, 
        ean: str,
        product_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Insert a product with EAN code into database.
        
        Args:
            section_id: Section ID where product will be placed
            ean: EAN code of the product
            product_data: Product data from external API
            
        Returns:
            Inserted product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Generate new UUID
            product_uuid = str(uuid4())
            
            # Insert new product into database with external API data
            cursor.execute("""
                INSERT INTO sale_reservations
                (uuid, section_id, ean, status, type, name, description, price,
                 age_control_required, cover_image, created_at, last_update)
                VALUES (%s, %s, %s, 1, 'ean', %s, %s, %s, %s, %s, %s, %s)
            """, (
                product_uuid,
                str(section_id),
                ean,
                product_data.get('name'),
                product_data.get('description'),
                product_data.get('price'),
                product_data.get('age_control_required'),
                product_data.get('cover_image'),
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()

            product_change_status_async(
                reservation_uuid=product_uuid,
                section_id=section_id,
                price=product_data.get('price'),
                ean=ean,
                action=1,
                status=1
            )
            
            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE uuid = %s
            """, (product_uuid,))
            
            return cursor.fetchone()
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error inserting product with EAN: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    async def remove_product(self, section_id: int) -> bool:
        """
        Remove product from section.
        
        Args:
            section_id: Section ID to remove product from
            
        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                UPDATE sale_reservations 
                SET status = 0, last_update = %s
                WHERE section_id = %s AND status != 0
            """, (datetime.now(), section_id))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error removing product: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def remove_all_products(self) -> bool:
        """
        Remove all products from all sections.
        
        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            if getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on"):
                cursor.execute("""
                    UPDATE sale_reservations 
                    SET status = 0, last_update = %s
                    WHERE status != 0
                """, (datetime.now(),))
                conn.commit()
            else:
                #TODO: Send product change status to Jetveo
                pass
            return True
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error removing all products: {err}")
            return False
        finally:
            cursor.close()
            conn.close()
    
    async def list_products(self) -> List[Dict[str, Any]]:
        """
        List all active products.
        
        Returns:
            List of active products
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE status != 0
                ORDER BY created_at DESC
            """)
            
            return cursor.fetchall()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error listing products: {err}")
            return []
        finally:
            cursor.close()
            conn.close()
    
    async def list_section_products(self, section_id: int) -> List[Dict[str, Any]]:
        """
        List products in specific section.
        
        Args:
            section_id: Section ID to list products from
            
        Returns:
            List of products in section
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT * FROM sale_reservations 
                WHERE section_id = %s AND status != 0
                ORDER BY created_at DESC
            """, (section_id,))
            
            return cursor.fetchall()
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error listing section products: {err}")
            return []
        finally:
            cursor.close()
            conn.close()
    
    async def reserve_section(
        self, 
        section_id: int, 
        reservation_pin: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Reserve a section with optional PIN.
        
        Args:
            section_id: Section ID to reserve
            reservation_pin: Optional reservation PIN
            
        Returns:
            Updated product record or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, check if there's a product with status=1 in the section
            cursor.execute("""
                SELECT id FROM sale_reservations 
                WHERE section_id = %s AND status = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No active product found in section {section_id}")
                return None
            
            # Generate 6-digit PIN if not provided
            if not reservation_pin:
                from .pin_generator import generate_pin
                reservation_pin = generate_pin()

                if reservation_pin is None:
                    self.logger.error("Failed to generate unique PIN for reservation")
                    return None
            
            self.logger.info(f"Generated PIN {reservation_pin} for section {section_id}")
            
            # Update product to reserved status
            cursor.execute("""
                UPDATE sale_reservations
                SET reserved = 1, reservation_pin = %s, last_update = %s
                WHERE section_id = %s AND status = 1
            """, (reservation_pin, datetime.now(), section_id))

            conn.commit()

            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE section_id = %s AND status = 1
                """, (section_id,))

                result = cursor.fetchone()

                # Send product change status to Jetveo
                if result:
                    product_change_status_async(
                        reservation_uuid=result['uuid'],
                        reserved=True,
                        reservation_pin=reservation_pin,
                        section_id=section_id
                    )

                self.logger.info(f"Successfully reserved section {section_id} with PIN {reservation_pin}")
                return result
            else:
                self.logger.error(f"No rows updated when reserving section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error reserving section: {err}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    # purchase_product method removed - no longer needed
    # Remaining purchase_product method code removed
    
    async def update_payment_status(self, section_id: int) -> bool:
        """
        Update payment status for a product in section.
        After successful payment, set status to 0 (deactivated).

        Args:
            section_id: Section ID to update payment status for

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # Get the product before updating
            cursor.execute("""
                SELECT uuid FROM sale_reservations
                WHERE section_id = %s AND status = 1
            """, (section_id,))

            product = cursor.fetchone()

            cursor.execute("""
                UPDATE sale_reservations
                SET paid_status = '1',
                    status = 0,
                    last_update = %s
                WHERE section_id = %s
                  AND status = 1
            """, (datetime.now(), section_id))

            conn.commit()

            # Send product change status to Jetveo
            if product:
                product_change_status_async(
                    reservation_uuid=product['uuid'],
                    section_id=section_id,
                    status=0
                )

            log_timeline_event(
                event_type="product_status_changed",
                event_result="sold",
                section_id=str(section_id),
                message="Payment status updated successfully"
            )
            return True
            
        except mysql.connector.Error as err:
            conn.rollback()
            log_timeline_event(
                event_type="update_product_status",
                event_result="failed",
                section_id=str(section_id),
                message=f"Database error updating payment status: {err}"
            )
            self.logger.error(f"Database error updating payment status: {err}")
            return False
        finally:
            cursor.close()
            conn.close()

    async def update_product_price(
        self, 
        section_id: int, 
        new_price: Decimal
    ) -> Optional[Dict[str, Any]]:
        """
        Update the price of a product in a specific section.
        
        Args:
            section_id: Section ID containing the product
            new_price: New price for the product
            
        Returns:
            Updated product record with old price or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, get the current product to check if it exists and get old price
            cursor.execute("""
                SELECT id, price FROM sale_reservations 
                WHERE section_id = %s AND status = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No active product found in section {section_id}")
                return None
            
            old_price = existing_product['price']
            
            # Update the price
            cursor.execute("""
                UPDATE sale_reservations
                SET price = %s, last_update = %s
                WHERE section_id = %s AND status = 1
            """, (float(new_price), datetime.now(), section_id))

            conn.commit()

            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations
                    WHERE section_id = %s AND status = 1
                """, (section_id,))

                result = cursor.fetchone()
                result['old_price'] = old_price  # Add old price to result

                # Send product change status to Jetveo
                if result:
                    product_change_status_async(
                        reservation_uuid=result['uuid'],
                        section_id=section_id,
                        price=float(new_price)
                    )

                self.logger.info(f"Successfully updated price in section {section_id} from {old_price} to {new_price}")
                return result
            else:
                self.logger.error(f"No rows updated when updating price in section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error updating product price: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    def update_sale_reservation_status(self, status: int, product_id: Optional[int] = None, section_id: Optional[int] = None) -> bool:
        """
        Update sale reservation status for a product.
        Used to mark product as picked up (status=0).

        Args:
            status: New status (0 = completed/picked up)
            product_id: Product ID to update (optional)
            section_id: Section ID containing the product (optional)

        Note:
            Either section_id or product_id must be provided, but not both.

        Returns:
            True if successful, False otherwise
        """
        # Validate input parameters
        if not product_id and not section_id:
            logger.error("Either product_id or section_id must be provided")
            return False

        if product_id and section_id:
            logger.error("Only one of product_id or section_id should be provided, not both")
            return False

        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            if product_id:
                # Update by product_id
                # Get the product UUID before updating
                cursor.execute("""
                    SELECT uuid, section_id FROM sale_reservations
                    WHERE id = %s
                """, (product_id,))

                product = cursor.fetchone()

                cursor.execute("""
                    UPDATE sale_reservations
                    SET status = %s, last_update = %s
                    WHERE id = %s
                """, (status, datetime.now(), product_id))

            elif section_id:
                # Update by section_id
                # Get all products in the section before updating
                cursor.execute("""
                    SELECT uuid, id FROM sale_reservations
                    WHERE section_id = %s AND status != 0
                """, (section_id,))

                products = cursor.fetchall()
                product = products[0] if products else None  # Use first product for logging

                cursor.execute("""
                    UPDATE sale_reservations
                    SET status = %s, last_update = %s
                    WHERE section_id = %s AND status != 0
                """, (status, datetime.now(), section_id))

            conn.commit()

            # Define identifier for logging
            identifier = product_id or section_id

            if cursor.rowcount > 0:
                # Send product change status to Jetveo
                if product and 'uuid' in product:
                    # For product_id updates, we need section_id from the fetched product
                    # For section_id updates, we use the provided section_id
                    product_section_id = product.get('section_id') if product_id else section_id
                    product_change_status_async(
                        reservation_uuid=product['uuid'],
                        section_id=product_section_id,
                        status=status
                    )

                self.logger.info(f"Successfully updated sale reservation {identifier} status to {status}")
                return True
            else:
                self.logger.warning(f"No rows updated when updating sale reservation {identifier} status")
                return False

        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error updating sale reservation status: {err}")
            return False
        finally:
            cursor.close()
            conn.close()

    async def cancel_reservation(
        self, 
        section_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Cancel a reservation for a section.
        
        Args:
            section_id: Section ID to cancel reservation for
            
        Returns:
            Updated product record with cancelled PIN or None if failed
        """
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # First, check if there's a reserved product in the section
            cursor.execute("""
                SELECT id, reservation_pin FROM sale_reservations 
                WHERE section_id = %s AND status = 1 AND reserved = 1
            """, (section_id,))
            
            existing_product = cursor.fetchone()
            if not existing_product:
                self.logger.error(f"No reserved product found in section {section_id}")
                return None
            
            cancelled_pin = existing_product['reservation_pin']
            
            # Cancel the reservation
            cursor.execute("""
                UPDATE sale_reservations 
                SET reserved = 0, reservation_pin = NULL, last_update = %s
                WHERE section_id = %s AND status = 1 AND reserved = 1
            """, (datetime.now(), section_id))
            
            conn.commit()
            
            if cursor.rowcount > 0:
                # Get the updated record
                cursor.execute("""
                    SELECT * FROM sale_reservations 
                    WHERE section_id = %s AND status = 1
                """, (section_id,))
                
                result = cursor.fetchone()
                result['cancelled_pin'] = cancelled_pin  # Add cancelled PIN to result
                
                self.logger.info(f"Successfully cancelled reservation in section {section_id}, PIN: {cancelled_pin}")
                return result
            else:
                self.logger.error(f"No rows updated when cancelling reservation in section {section_id}")
                return None
            
        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error cancelling reservation: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str):
        """Handle section open for product endpoints"""
        match endpoint_type:
            case "product/pickup":
                return self.update_sale_reservation_status(status=0, section_id=section_id)     # deactivate product reservation
            case _:
                pass  # No action needed for other endpoints





# Global repository instance
product_repository = ProductRepository()
